# Flask Core
Flask>=2.3.0
Flask-PyMongo>=2.3.0
Flask-RESTX>=1.1.0
Flask-JWT-Extended>=4.4.0
Flask-CORS>=4.0.0

# Database
pymongo>=4.3.0

# Data Validation & Serialization
marshmallow>=3.19.0

# Authentication & Security
PyJWT>=2.6.0
bcrypt>=3.2.0
cryptography>=3.4.8

# Configuration & Environment
python-dotenv>=0.19.0

# Date & Time
python-dateutil>=2.8.0

# HTTP & Requests
requests>=2.28.0

# Logging
structlog>=22.1.0

# Testing
pytest>=7.0.0
pytest-flask>=1.2.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0

# Development Tools
black>=22.0.0
flake8>=5.0.0
isort>=5.10.0

# Documentation
sphinx>=5.0.0
sphinx-rtd-theme>=1.0.0

# Monitoring & Health
prometheus-flask-exporter>=0.20.0

# Utilities
click>=8.0.0
python-json-logger>=2.0.0

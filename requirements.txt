# Flask Core
Flask==2.3.3
Flask-PyMongo==2.3.0
Flask-RESTX==1.2.0
Flask-JWT-Extended==4.5.3
Flask-CORS==4.0.0

# Database
pymongo==4.5.0

# Data Validation & Serialization
marshmallow==3.20.1
marshmallow-mongoengine==0.31.0

# Authentication & Security
PyJWT==2.8.0
bcrypt==4.0.1
cryptography==41.0.4

# Configuration & Environment
python-dotenv==1.0.0

# Date & Time
python-dateutil==2.8.2

# HTTP & Requests
requests==2.31.0

# Logging
structlog==23.1.0

# Testing
pytest==7.4.2
pytest-flask==1.2.0
pytest-cov==4.1.0
pytest-mock==3.11.1

# Development Tools
black==23.7.0
flake8==6.0.0
isort==5.12.0

# Documentation
sphinx==7.1.2
sphinx-rtd-theme==1.3.0

# Monitoring & Health
prometheus-flask-exporter==0.23.0

# Utilities
click==8.1.7
python-json-logger==2.0.7

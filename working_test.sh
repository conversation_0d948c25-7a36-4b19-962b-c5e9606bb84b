#!/bin/bash

# 血糖监测API工作测试脚本 (使用简化接口)
# Working Glucose Monitoring API Test Script

API_BASE="http://***********:5000"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 血糖监测API工作测试 (简化接口) ===${NC}"
echo -e "${BLUE}API服务器: $API_BASE${NC}"
echo ""

# 1. 健康检查
echo -e "${YELLOW}1. 健康检查...${NC}"
response=$(curl -s $API_BASE/)
echo -e "${GREEN}✓ 服务器正常运行${NC}"
echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
echo ""

# 2. 数据库状态检查
echo -e "${YELLOW}2. 数据库状态检查...${NC}"
response=$(curl -s $API_BASE/db-status)
echo -e "${GREEN}✓ 数据库连接正常${NC}"
echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
echo ""

# 3. 上传血糖数据
echo -e "${YELLOW}3. 上传血糖数据...${NC}"

# 测试数据数组
declare -a test_data=(
    '{"user_id":"patient_001","timestamp":"2025-06-04T08:00:00Z","glucose_value":5.2,"unit":"mmol/L","device_id":"meter_001","note":"空腹血糖"}'
    '{"user_id":"patient_001","timestamp":"2025-06-04T10:30:00Z","glucose_value":7.8,"unit":"mmol/L","device_id":"meter_001","note":"早餐后2小时"}'
    '{"user_id":"patient_001","timestamp":"2025-06-04T12:00:00Z","glucose_value":6.1,"unit":"mmol/L","device_id":"meter_001","note":"午餐前"}'
    '{"user_id":"patient_001","timestamp":"2025-06-04T14:30:00Z","glucose_value":8.5,"unit":"mmol/L","device_id":"meter_001","note":"午餐后2小时"}'
    '{"user_id":"patient_001","timestamp":"2025-06-04T18:00:00Z","glucose_value":6.8,"unit":"mmol/L","device_id":"meter_001","note":"晚餐前"}'
    '{"user_id":"patient_002","timestamp":"2025-06-04T09:00:00Z","glucose_value":110,"unit":"mg/dL","device_id":"cgm_002","note":"连续血糖监测"}'
    '{"user_id":"patient_002","timestamp":"2025-06-04T11:00:00Z","glucose_value":145,"unit":"mg/dL","device_id":"cgm_002","note":"餐后血糖"}'
)

# 存储创建的记录ID
record_ids=()

for i in "${!test_data[@]}"; do
    echo -e "  ${BLUE}上传记录 $((i+1))...${NC}"
    
    response=$(curl -s -w "%{http_code}" -X POST $API_BASE/simple-glucose \
        -H "Content-Type: application/json" \
        -d "${test_data[$i]}")
    
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "201" ]; then
        echo -e "    ${GREEN}✓ 上传成功${NC}"
        # 提取记录ID
        record_id=$(echo "$body" | python3 -c "import sys, json; print(json.load(sys.stdin)['data']['id'])" 2>/dev/null)
        if [ "$record_id" != "" ]; then
            record_ids+=("$record_id")
            echo "    记录ID: $record_id"
        fi
        # 显示血糖值
        glucose_value=$(echo "$body" | python3 -c "import sys, json; print(json.load(sys.stdin)['data']['glucose_value'])" 2>/dev/null)
        unit=$(echo "$body" | python3 -c "import sys, json; print(json.load(sys.stdin)['data']['unit'])" 2>/dev/null)
        note=$(echo "$body" | python3 -c "import sys, json; print(json.load(sys.stdin)['data']['note'])" 2>/dev/null)
        echo "    血糖值: $glucose_value $unit - $note"
    else
        echo -e "    ${RED}✗ 上传失败 (HTTP $http_code)${NC}"
        echo "    $body" | python3 -m json.tool 2>/dev/null || echo "    $body"
    fi
done

echo ""

# 4. 查询血糖记录
echo -e "${YELLOW}4. 查询血糖记录...${NC}"

echo -e "  ${BLUE}查询患者 patient_001 的所有记录...${NC}"
response=$(curl -s -w "%{http_code}" -X GET "$API_BASE/simple-glucose?user_id=patient_001")
http_code="${response: -3}"
body="${response%???}"

if [ "$http_code" = "200" ]; then
    echo -e "    ${GREEN}✓ 查询成功${NC}"
    record_count=$(echo "$body" | python3 -c "import sys, json; print(len(json.load(sys.stdin)['data']['records']))" 2>/dev/null)
    echo "    找到 $record_count 条记录"
    
    # 显示记录摘要
    echo "    记录摘要:"
    echo "$body" | python3 -c "
import sys, json
data = json.load(sys.stdin)
for record in data['data']['records']:
    print(f\"      {record['timestamp']}: {record['glucose_value']} {record['unit']} - {record['note']}\")
" 2>/dev/null
else
    echo -e "    ${RED}✗ 查询失败 (HTTP $http_code)${NC}"
    echo "    $body"
fi

echo ""

echo -e "  ${BLUE}查询患者 patient_002 的所有记录...${NC}"
response=$(curl -s -w "%{http_code}" -X GET "$API_BASE/simple-glucose?user_id=patient_002")
http_code="${response: -3}"
body="${response%???}"

if [ "$http_code" = "200" ]; then
    echo -e "    ${GREEN}✓ 查询成功${NC}"
    record_count=$(echo "$body" | python3 -c "import sys, json; print(len(json.load(sys.stdin)['data']['records']))" 2>/dev/null)
    echo "    找到 $record_count 条记录"
    
    # 显示记录摘要
    echo "    记录摘要:"
    echo "$body" | python3 -c "
import sys, json
data = json.load(sys.stdin)
for record in data['data']['records']:
    print(f\"      {record['timestamp']}: {record['glucose_value']} {record['unit']} - {record['note']}\")
" 2>/dev/null
else
    echo -e "    ${RED}✗ 查询失败 (HTTP $http_code)${NC}"
    echo "    $body"
fi

echo ""

# 5. 错误处理测试
echo -e "${YELLOW}5. 错误处理测试...${NC}"
echo -e "  ${BLUE}测试缺少必需字段...${NC}"

invalid_data='{"user_id":"test_user","glucose_value":6.5}'

response=$(curl -s -w "%{http_code}" -X POST $API_BASE/simple-glucose \
    -H "Content-Type: application/json" \
    -d "$invalid_data")

http_code="${response: -3}"
body="${response%???}"

if [ "$http_code" = "400" ]; then
    echo -e "    ${GREEN}✓ 正确返回验证错误${NC}"
    echo "    $body" | python3 -m json.tool 2>/dev/null || echo "    $body"
else
    echo -e "    ${RED}✗ 错误处理异常 (HTTP $http_code)${NC}"
    echo "    $body"
fi

echo ""

# 6. 最终统计
echo -e "${YELLOW}6. 最终数据统计...${NC}"

for user in "patient_001" "patient_002"; do
    response=$(curl -s "$API_BASE/simple-glucose?user_id=$user")
    if [ $? -eq 0 ]; then
        total_records=$(echo "$response" | python3 -c "import sys, json; print(len(json.load(sys.stdin)['data']['records']))" 2>/dev/null)
        echo -e "  ${GREEN}患者 $user 总共有 $total_records 条血糖记录${NC}"
    fi
done

echo ""
echo -e "${BLUE}=== 测试完成 ===${NC}"
echo -e "${GREEN}✓ 血糖监测API测试成功！数据已保存到MongoDB${NC}"
echo ""
echo "您可以使用以下命令查看数据:"
echo "curl \"$API_BASE/simple-glucose?user_id=patient_001\" | python3 -m json.tool"
echo "curl \"$API_BASE/simple-glucose?user_id=patient_002\" | python3 -m json.tool"
echo ""
echo "数据库中的记录ID列表:"
for id in "${record_ids[@]}"; do
    echo "  $id"
done

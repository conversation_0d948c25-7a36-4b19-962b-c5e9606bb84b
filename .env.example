# Flask配置
FLASK_CONFIG=development
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=True

# 安全密钥
SECRET_KEY=your-super-secret-key-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-in-production

# 数据库配置
MONGO_URI=mongodb://localhost:27017/glucose_db

# 开发环境数据库
DEV_MONGO_URI=mongodb://localhost:27017/glucose_dev

# 测试环境数据库
TEST_MONGO_URI=mongodb://localhost:27017/glucose_test

# Redis配置 (可选)
REDIS_URL=redis://localhost:6379/0

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/glucose_api.log

# 邮件配置 (可选)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password

# 文件上传配置
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads

# 监控配置 (可选)
SENTRY_DSN=your-sentry-dsn

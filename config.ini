# 血糖监测系统配置文件
# Glucose Monitoring System Configuration File

[DEFAULT]
# 基础配置
secret_key = dev-secret-key-change-in-production
jwt_secret_key = jwt-secret-key-change-in-production

# 服务器配置
host = 0.0.0.0
port = 5000
debug = true

# 数据库配置
mongo_uri = mongodb://localhost:27017/glucose_db

# 日志配置
log_level = INFO
log_file = logs/glucose_api.log

# 分页配置
default_page_size = 20
max_page_size = 100

# 数据验证配置
max_glucose_value = 50.0
min_glucose_value = 0.1

[development]
# 开发环境配置
debug = true
log_level = DEBUG
mongo_uri = mongodb://localhost:27017/glucose_dev

[testing]
# 测试环境配置
debug = false
testing = true
mongo_uri = mongodb://localhost:27017/glucose_test
jwt_access_token_expires_minutes = 15

[production]
# 生产环境配置
debug = false
testing = false
secret_key = production-secret-key-change-this
jwt_secret_key = production-jwt-secret-key-change-this
mongo_uri = mongodb://localhost:27017/glucose_production
log_level = WARNING
session_cookie_secure = true
session_cookie_httponly = true

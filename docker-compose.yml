version: '3.8'

services:
  # MongoDB 数据库服务
  mongodb:
    image: mongo:6.0
    container_name: glucose-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: glucose_db
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongodb/init:/docker-entrypoint-initdb.d
    networks:
      - glucose-network

  # Redis 缓存服务 (可选)
  redis:
    image: redis:7-alpine
    container_name: glucose-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - glucose-network

  # Flask API 服务
  api:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: glucose-api
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - FLASK_CONFIG=production
      - MONGO_URI=*********************************************************************
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./logs:/app/logs
    networks:
      - glucose-network

  # Nginx 反向代理 (生产环境)
  nginx:
    image: nginx:alpine
    container_name: glucose-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api
    networks:
      - glucose-network

volumes:
  mongodb_data:
  redis_data:

networks:
  glucose-network:
    driver: bridge
